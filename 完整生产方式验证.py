"""
完整验证所有可能的生产方式组合
不预设任何假设，让数学模型自己找到最优解
"""

import numpy as np
from scipy.optimize import linprog
import itertools

def solve_complete_model():
    """
    求解完整的线性规划模型，包含所有决策变量
    """
    print("=" * 60)
    print("完整生产方式验证 - 不预设任何假设")
    print("=" * 60)
    
    # 决策变量: [x1, x2, y1, y2, z1, z2]
    # x1: 甲类设备加工牛奶桶数
    # x2: 乙类设备加工牛奶桶数
    # y1: A1产品直接销售数量(kg)
    # y2: A2产品直接销售数量(kg)  
    # z1: A1产品深加工数量(kg)
    # z2: A2产品深加工数量(kg)
    
    # 目标函数系数 (最大化问题，需要取负号)
    # Z = 12*y1 + 8*y2 + 22*0.8*z1 + 16*0.75*z2 - 1.5*z1 - 1.5*z2
    # Z = 12*y1 + 8*y2 + 16.1*z1 + 10.5*z2
    c = [-12, -8, -16.1, -10.5, 0, 0]  # 加上x1,x2的系数0
    
    # 重新排列：[x1, x2, y1, y2, z1, z2]
    c = [0, 0, -12, -8, -16.1, -10.5]
    
    # 约束矩阵 A_ub * x <= b_ub
    A_ub = [
        # 原料约束: x1 + x2 <= 50
        [1, 1, 0, 0, 0, 0],
        
        # 劳动时间约束: 12*x1 + 8*x2 + 2*z1 + 2*z2 <= 480
        [12, 8, 0, 0, 2, 2],
        
        # 甲类设备约束: 3*x1 <= 100
        [3, 0, 0, 0, 0, 0],
    ]
    
    b_ub = [50, 480, 100]
    
    # 等式约束 A_eq * x = b_eq
    A_eq = [
        # A1产品平衡: y1 + z1 = 3*x1
        [-3, 0, 1, 0, 1, 0],
        
        # A2产品平衡: y2 + z2 = 4*x2  
        [0, -4, 0, 1, 0, 1],
    ]
    
    b_eq = [0, 0]
    
    # 变量边界 (所有变量非负)
    bounds = [(0, None)] * 6
    
    # 求解
    result = linprog(c, A_ub=A_ub, b_ub=b_ub, A_eq=A_eq, b_eq=b_eq, 
                     bounds=bounds, method='highs')
    
    if result.success:
        x1, x2, y1, y2, z1, z2 = result.x
        optimal_value = -result.fun
        
        print(f"完整模型求解成功！")
        print(f"\n最优解:")
        print(f"  甲类设备加工牛奶: {x1:.2f} 桶")
        print(f"  乙类设备加工牛奶: {x2:.2f} 桶")
        print(f"  A1直接销售: {y1:.2f} kg")
        print(f"  A2直接销售: {y2:.2f} kg") 
        print(f"  A1深加工: {z1:.2f} kg")
        print(f"  A2深加工: {z2:.2f} kg")
        print(f"  最大净利润: {optimal_value:.2f} 元/天")
        
        # 验证产品平衡
        a1_total = 3 * x1
        a2_total = 4 * x2
        print(f"\n产品平衡验证:")
        print(f"  A1总产量: {a1_total:.2f} kg = A1销售({y1:.2f}) + A1深加工({z1:.2f})")
        print(f"  A2总产量: {a2_total:.2f} kg = A2销售({y2:.2f}) + A2深加工({z2:.2f})")
        
        # 计算深加工产品
        b1_output = 0.8 * z1
        b2_output = 0.75 * z2
        print(f"\n深加工产品:")
        print(f"  B1产量: {b1_output:.2f} kg")
        print(f"  B2产量: {b2_output:.2f} kg")
        
        # 验证约束
        print(f"\n约束验证:")
        print(f"  原料使用: {x1 + x2:.2f} / 50 桶")
        print(f"  劳动时间: {12*x1 + 8*x2 + 2*z1 + 2*z2:.2f} / 480 小时")
        print(f"  甲类设备: {3*x1:.2f} / 100 kg A1")
        
        return result.x, optimal_value
    else:
        print("求解失败!")
        print(f"失败原因: {result.message}")
        return None, None

def analyze_production_strategies():
    """
    分析不同的生产策略
    """
    print("\n" + "=" * 60)
    print("分析不同生产策略的利润")
    print("=" * 60)
    
    strategies = [
        ("纯甲类生产+全部直接销售", lambda: pure_strategy(1, 0, 0, 0)),
        ("纯甲类生产+全部深加工", lambda: pure_strategy(1, 0, 1, 1)),
        ("纯乙类生产+全部直接销售", lambda: pure_strategy(0, 1, 0, 0)),
        ("纯乙类生产+全部深加工", lambda: pure_strategy(0, 1, 1, 1)),
        ("混合生产+全部直接销售", lambda: mixed_strategy(0, 0)),
        ("混合生产+全部深加工", lambda: mixed_strategy(1, 1)),
        ("甲类直销+乙类深加工", lambda: mixed_strategy(0, 1)),
        ("甲类深加工+乙类直销", lambda: mixed_strategy(1, 0)),
    ]
    
    results = []
    for name, strategy_func in strategies:
        try:
            profit, details = strategy_func()
            results.append((name, profit, details))
            print(f"\n{name}:")
            print(f"  利润: {profit:.2f} 元")
            print(f"  详情: {details}")
        except Exception as e:
            print(f"\n{name}: 不可行 ({e})")
            results.append((name, -float('inf'), "不可行"))
    
    # 找到最优策略
    best_strategy = max(results, key=lambda x: x[1] if x[1] != -float('inf') else -float('inf'))
    print(f"\n" + "="*40)
    print(f"最优策略: {best_strategy[0]}")
    print(f"最大利润: {best_strategy[1]:.2f} 元")
    print(f"="*40)
    
    return results

def pure_strategy(use_type1, use_type2, deep1_ratio, deep2_ratio):
    """
    纯策略分析
    use_type1: 是否使用甲类设备 (0或1)
    use_type2: 是否使用乙类设备 (0或1) 
    deep1_ratio: A1深加工比例 (0-1)
    deep2_ratio: A2深加工比例 (0-1)
    """
    if use_type1 and use_type2:
        raise ValueError("纯策略不能同时使用两种设备")
    
    if use_type1:
        # 纯甲类策略
        # 受限于: min(50桶原料, 480/18=26.67桶劳动时间, 100/3=33.33桶设备)
        max_x1 = min(50, 480/18, 100/3)
        x1, x2 = max_x1, 0
        a1_total = 3 * x1
        z1 = deep1_ratio * a1_total
        y1 = (1 - deep1_ratio) * a1_total
        z2 = y2 = 0
        
    elif use_type2:
        # 纯乙类策略  
        # 受限于: min(50桶原料, 480/16=30桶劳动时间)
        max_x2 = min(50, 480/16)
        x1, x2 = 0, max_x2
        a2_total = 4 * x2
        z2 = deep2_ratio * a2_total
        y2 = (1 - deep2_ratio) * a2_total
        z1 = y1 = 0
    else:
        raise ValueError("必须选择一种设备")
    
    # 计算利润
    profit = 12*y1 + 8*y2 + 16.1*z1 + 10.5*z2
    
    # 验证劳动时间约束
    labor_used = 12*x1 + 8*x2 + 2*z1 + 2*z2
    if labor_used > 480:
        raise ValueError(f"劳动时间超限: {labor_used} > 480")
    
    details = f"x1={x1:.1f}, x2={x2:.1f}, y1={y1:.1f}, y2={y2:.1f}, z1={z1:.1f}, z2={z2:.1f}"
    return profit, details

def mixed_strategy(deep1_ratio, deep2_ratio):
    """
    混合策略分析 - 同时使用两种设备
    """
    # 这需要求解一个简化的线性规划问题
    # 目标: 最大化利润，在劳动时间约束下分配x1和x2
    
    # 甲类每桶的总利润系数
    coeff1 = 3 * (12 * (1-deep1_ratio) + 16.1 * deep1_ratio)
    # 乙类每桶的总利润系数  
    coeff2 = 4 * (8 * (1-deep2_ratio) + 10.5 * deep2_ratio)
    
    # 甲类每桶的总劳动时间
    labor1 = 12 + 2 * 3 * deep1_ratio
    # 乙类每桶的总劳动时间
    labor2 = 8 + 2 * 4 * deep2_ratio
    
    # 求解: max coeff1*x1 + coeff2*x2
    # s.t. x1 + x2 <= 50
    #      labor1*x1 + labor2*x2 <= 480
    #      3*x1 <= 100
    #      x1, x2 >= 0
    
    c = [-coeff1, -coeff2]
    A = [[1, 1], [labor1, labor2], [3, 0]]
    b = [50, 480, 100]
    bounds = [(0, None), (0, None)]
    
    result = linprog(c, A_ub=A, b_ub=b, bounds=bounds, method='highs')
    
    if result.success:
        x1, x2 = result.x
        profit = -result.fun
        
        # 计算各产品数量
        a1_total = 3 * x1
        a2_total = 4 * x2
        z1 = deep1_ratio * a1_total
        y1 = (1 - deep1_ratio) * a1_total
        z2 = deep2_ratio * a2_total
        y2 = (1 - deep2_ratio) * a2_total
        
        details = f"x1={x1:.1f}, x2={x2:.1f}, y1={y1:.1f}, y2={y2:.1f}, z1={z1:.1f}, z2={z2:.1f}"
        return profit, details
    else:
        raise ValueError("混合策略求解失败")

if __name__ == "__main__":
    # 完整模型求解
    optimal_solution, optimal_profit = solve_complete_model()
    
    # 分析不同策略
    strategy_results = analyze_production_strategies()
    
    print(f"\n" + "=" * 60)
    print("结论验证")
    print("=" * 60)
    
    if optimal_solution is not None:
        print(f"完整模型最优利润: {optimal_profit:.2f} 元")
        
        # 检查是否与策略分析一致
        best_strategy_profit = max(r[1] for r in strategy_results if r[1] != -float('inf'))
        print(f"策略分析最优利润: {best_strategy_profit:.2f} 元")
        
        if abs(optimal_profit - best_strategy_profit) < 0.01:
            print("✅ 验证通过：两种方法得到相同结果")
        else:
            print("❌ 验证失败：结果不一致，需要进一步检查")
    
    print(f"\n所有策略利润排序:")
    sorted_results = sorted(strategy_results, key=lambda x: x[1] if x[1] != -float('inf') else -float('inf'), reverse=True)
    for i, (name, profit, details) in enumerate(sorted_results):
        if profit != -float('inf'):
            print(f"{i+1}. {name}: {profit:.2f} 元")
        else:
            print(f"{i+1}. {name}: 不可行")
