"""
标准线性规划方法求解奶制品生产优化问题
严格按照线性规划的数学框架和方法
"""

import numpy as np
from scipy.optimize import linprog
import pandas as pd

def formulate_linear_programming_model():
    """
    建立标准线性规划模型
    """
    print("=" * 60)
    print("线性规划模型建立")
    print("=" * 60)
    
    print("📋 决策变量定义:")
    print("x1 = 甲类设备加工的牛奶桶数")
    print("x2 = 乙类设备加工的牛奶桶数")
    print("y1 = A1产品直接销售的数量(kg)")
    print("y2 = A2产品直接销售的数量(kg)")
    print("z1 = A1产品用于深加工的数量(kg)")
    print("z2 = A2产品用于深加工的数量(kg)")
    
    print("\n📈 目标函数:")
    print("max Z = 12y1 + 8y2 + (22×0.8-1.5)z1 + (16×0.75-1.5)z2")
    print("max Z = 12y1 + 8y2 + 16.1z1 + 10.5z2")
    
    print("\n📊 约束条件:")
    print("1. 原料约束: x1 + x2 ≤ 50")
    print("2. 劳动时间约束: 12x1 + 8x2 + 2z1 + 2z2 ≤ 480")
    print("3. 甲类设备约束: 3x1 ≤ 100")
    print("4. A1产品平衡: y1 + z1 = 3x1")
    print("5. A2产品平衡: y2 + z2 = 4x2")
    print("6. 非负约束: x1,x2,y1,y2,z1,z2 ≥ 0")

def solve_linear_programming():
    """
    使用scipy.optimize.linprog求解线性规划问题
    """
    print("\n" + "=" * 60)
    print("线性规划求解")
    print("=" * 60)
    
    # 决策变量顺序: [x1, x2, y1, y2, z1, z2]
    
    # 目标函数系数 (linprog求最小值，所以取负号)
    c = [0, 0, -12, -8, -16.1, -10.5]
    
    # 不等式约束矩阵 A_ub × x ≤ b_ub
    A_ub = np.array([
        [1,  1,  0, 0,  0,  0],    # x1 + x2 ≤ 50
        [12, 8,  0, 0,  2,  2],    # 12x1 + 8x2 + 2z1 + 2z2 ≤ 480
        [3,  0,  0, 0,  0,  0],    # 3x1 ≤ 100
    ])
    
    b_ub = np.array([50, 480, 100])
    
    # 等式约束矩阵 A_eq × x = b_eq
    A_eq = np.array([
        [-3, 0, 1, 0, 1, 0],       # -3x1 + y1 + z1 = 0 (即 y1 + z1 = 3x1)
        [0, -4, 0, 1, 0, 1],       # -4x2 + y2 + z2 = 0 (即 y2 + z2 = 4x2)
    ])
    
    b_eq = np.array([0, 0])
    
    # 变量边界 (所有变量非负)
    bounds = [(0, None) for _ in range(6)]
    
    print("🔧 求解参数:")
    print(f"目标函数系数 c = {c}")
    print(f"不等式约束矩阵 A_ub 形状: {A_ub.shape}")
    print(f"不等式约束右端 b_ub = {b_ub}")
    print(f"等式约束矩阵 A_eq 形状: {A_eq.shape}")
    print(f"等式约束右端 b_eq = {b_eq}")
    
    # 使用linprog求解
    print("\n🚀 开始求解...")
    result = linprog(c, A_ub=A_ub, b_ub=b_ub, A_eq=A_eq, b_eq=b_eq, 
                     bounds=bounds, method='highs', options={'disp': True})
    
    return result

def analyze_solution(result):
    """
    分析线性规划求解结果
    """
    print("\n" + "=" * 60)
    print("求解结果分析")
    print("=" * 60)
    
    if not result.success:
        print("❌ 求解失败!")
        print(f"失败原因: {result.message}")
        return None
    
    # 提取最优解
    x1, x2, y1, y2, z1, z2 = result.x
    optimal_value = -result.fun  # 因为求解时取了负号
    
    print("✅ 求解成功!")
    print(f"\n📊 最优解:")
    print(f"x1* = {x1:.6f} (甲类设备加工牛奶桶数)")
    print(f"x2* = {x2:.6f} (乙类设备加工牛奶桶数)")
    print(f"y1* = {y1:.6f} (A1直接销售kg)")
    print(f"y2* = {y2:.6f} (A2直接销售kg)")
    print(f"z1* = {z1:.6f} (A1深加工kg)")
    print(f"z2* = {z2:.6f} (A2深加工kg)")
    print(f"Z* = {optimal_value:.2f} (最大净利润元/天)")
    
    # 验证约束条件
    print(f"\n🔍 约束条件验证:")
    
    # 不等式约束
    constraint1 = x1 + x2
    constraint2 = 12*x1 + 8*x2 + 2*z1 + 2*z2
    constraint3 = 3*x1
    
    print(f"1. 原料约束: {constraint1:.6f} ≤ 50 {'✓' if constraint1 <= 50.001 else '✗'}")
    print(f"2. 劳动时间约束: {constraint2:.6f} ≤ 480 {'✓' if constraint2 <= 480.001 else '✗'}")
    print(f"3. 甲类设备约束: {constraint3:.6f} ≤ 100 {'✓' if constraint3 <= 100.001 else '✗'}")
    
    # 等式约束
    balance1 = y1 + z1 - 3*x1
    balance2 = y2 + z2 - 4*x2
    
    print(f"4. A1产品平衡: {balance1:.6f} = 0 {'✓' if abs(balance1) < 0.001 else '✗'}")
    print(f"5. A2产品平衡: {balance2:.6f} = 0 {'✓' if abs(balance2) < 0.001 else '✗'}")
    
    # 识别紧约束
    print(f"\n🔴 紧约束识别:")
    tight_constraints = []
    
    if abs(constraint1 - 50) < 0.001:
        tight_constraints.append("原料约束")
    if abs(constraint2 - 480) < 0.001:
        tight_constraints.append("劳动时间约束")
    if abs(constraint3 - 100) < 0.001:
        tight_constraints.append("甲类设备约束")
    
    if tight_constraints:
        print(f"紧约束: {', '.join(tight_constraints)}")
    else:
        print("无紧约束")
    
    # 生产方案解释
    print(f"\n📈 生产方案解释:")
    a1_total = 3 * x1
    a2_total = 4 * x2
    
    print(f"甲类设备生产: {x1:.2f}桶牛奶 → {a1_total:.2f}kg A1")
    if a1_total > 0:
        print(f"  - A1直接销售: {y1:.2f}kg ({y1/a1_total:.1%})")
        print(f"  - A1深加工: {z1:.2f}kg ({z1/a1_total:.1%})")
    
    print(f"乙类设备生产: {x2:.2f}桶牛奶 → {a2_total:.2f}kg A2")
    if a2_total > 0:
        print(f"  - A2直接销售: {y2:.2f}kg ({y2/a2_total:.1%})")
        print(f"  - A2深加工: {z2:.2f}kg ({z2/a2_total:.1%})")
    
    # 最终产品
    b1_output = 0.8 * z1
    b2_output = 0.75 * z2
    
    print(f"\n🏭 最终产品产量:")
    print(f"A1直接销售: {y1:.2f}kg")
    print(f"A2直接销售: {y2:.2f}kg")
    print(f"B1深加工产品: {b1_output:.2f}kg")
    print(f"B2深加工产品: {b2_output:.2f}kg")
    
    # 利润构成
    profit_breakdown = {
        'A1直销': 12 * y1,
        'A2直销': 8 * y2,
        'B1深加工': 16.1 * z1,
        'B2深加工': 10.5 * z2
    }
    
    print(f"\n💰 利润构成分析:")
    total_check = 0
    for source, profit in profit_breakdown.items():
        print(f"{source}: {profit:.2f}元")
        total_check += profit
    print(f"总计: {total_check:.2f}元 (验证: {abs(total_check - optimal_value) < 0.01})")
    
    return result.x, optimal_value, tight_constraints

def sensitivity_analysis(optimal_solution, optimal_value):
    """
    敏感性分析 - 计算影子价格
    """
    print("\n" + "=" * 60)
    print("敏感性分析")
    print("=" * 60)
    
    x1_opt, x2_opt, y1_opt, y2_opt, z1_opt, z2_opt = optimal_solution
    
    # 原始约束
    c = [0, 0, -12, -8, -16.1, -10.5]
    A_eq = np.array([[-3, 0, 1, 0, 1, 0], [0, -4, 0, 1, 0, 1]])
    b_eq = np.array([0, 0])
    bounds = [(0, None) for _ in range(6)]
    
    print("📈 问题1: 原料供应投资分析")
    # 原料从50增加到51
    A_ub_new = np.array([
        [1,  1,  0, 0,  0,  0],    # x1 + x2 ≤ 51
        [12, 8,  0, 0,  2,  2],    # 劳动时间约束
        [3,  0,  0, 0,  0,  0],    # 甲类设备约束
    ])
    b_ub_new = np.array([51, 480, 100])
    
    result_material = linprog(c, A_ub=A_ub_new, b_ub=b_ub_new, A_eq=A_eq, b_eq=b_eq, bounds=bounds, method='highs')
    
    if result_material.success:
        new_profit = -result_material.fun
        shadow_price_material = new_profit - optimal_value
        print(f"原料影子价格: {shadow_price_material:.2f} 元/桶")
        print(f"投资15元增加1桶牛奶的净收益: {shadow_price_material - 15:.2f} 元")
        print(f"建议: {'投资' if shadow_price_material >= 15 else '不投资'}")
    
    print(f"\n⏰ 问题2: 劳动力成本分析")
    # 劳动时间从480增加到481
    A_ub_labor = np.array([
        [1,  1,  0, 0,  0,  0],    # 原料约束
        [12, 8,  0, 0,  2,  2],    # x1 + x2 ≤ 481
        [3,  0,  0, 0,  0,  0],    # 甲类设备约束
    ])
    b_ub_labor = np.array([50, 481, 100])
    
    result_labor = linprog(c, A_ub=A_ub_labor, b_ub=b_ub_labor, A_eq=A_eq, b_eq=b_eq, bounds=bounds, method='highs')
    
    if result_labor.success:
        new_profit_labor = -result_labor.fun
        shadow_price_labor = new_profit_labor - optimal_value
        print(f"劳动时间影子价格: {shadow_price_labor:.2f} 元/小时")
        print(f"临时工工资上限: {shadow_price_labor:.2f} 元/小时")
    
    print(f"\n📊 问题3: 价格波动分析")
    # B1、B2价格±10%波动
    scenarios = [
        ("价格+10%", [0, 0, -12, -8, -16.1*1.1, -10.5*1.1]),
        ("价格-10%", [0, 0, -12, -8, -16.1*0.9, -10.5*0.9])
    ]
    
    A_ub_orig = np.array([[1, 1, 0, 0, 0, 0], [12, 8, 0, 0, 2, 2], [3, 0, 0, 0, 0, 0]])
    b_ub_orig = np.array([50, 480, 100])
    
    for scenario_name, c_scenario in scenarios:
        result_scenario = linprog(c_scenario, A_ub=A_ub_orig, b_ub=b_ub_orig, A_eq=A_eq, b_eq=b_eq, bounds=bounds, method='highs')
        
        if result_scenario.success:
            profit_scenario = -result_scenario.fun
            x1_s, x2_s, y1_s, y2_s, z1_s, z2_s = result_scenario.x
            
            print(f"{scenario_name}:")
            print(f"  利润: {profit_scenario:.2f} 元")
            print(f"  生产方案: x1={x1_s:.1f}, x2={x2_s:.1f}, z1={z1_s:.1f}, z2={z2_s:.1f}")
            
            # 检查方案是否显著改变
            tolerance = 0.1
            plan_changed = (abs(x1_s - x1_opt) > tolerance or abs(x2_s - x2_opt) > tolerance or
                           abs(z1_s - z1_opt) > tolerance or abs(z2_s - z2_opt) > tolerance)
            
            print(f"  方案变化: {'是' if plan_changed else '否'}")

def main():
    """
    主函数 - 完整的线性规划求解流程
    """
    print("🎯 奶制品生产优化问题 - 标准线性规划求解")
    print("=" * 60)
    
    # 1. 建立模型
    formulate_linear_programming_model()
    
    # 2. 求解
    result = solve_linear_programming()
    
    # 3. 分析结果
    if result.success:
        optimal_solution, optimal_value, tight_constraints = analyze_solution(result)
        
        # 4. 敏感性分析
        sensitivity_analysis(optimal_solution, optimal_value)
        
        print(f"\n" + "🎯" * 20)
        print(f"线性规划求解完成")
        print(f"最优目标函数值: {optimal_value:.2f} 元/天")
        print(f"🎯" * 20)
        
        return optimal_solution, optimal_value
    else:
        print("求解失败，请检查模型设置")
        return None, None

if __name__ == "__main__":
    optimal_solution, optimal_value = main()
