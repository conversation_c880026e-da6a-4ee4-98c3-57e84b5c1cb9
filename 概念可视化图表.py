"""
为大一学生制作的概念可视化图表
用简单的图表解释影子价格、瓶颈约束等概念
"""

import matplotlib.pyplot as plt
import numpy as np
import matplotlib

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

def create_concept_visualization():
    """
    创建概念可视化图表
    """
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 图1：资源使用情况（瓶颈分析）
    resources = ['牛奶\n(桶)', '工人时间\n(小时)', '甲类设备\n(kg A1)']
    available = [50, 480, 100]
    used = [26.67, 480, 80]
    
    x = np.arange(len(resources))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, available, width, label='可用量', color='lightblue', alpha=0.7)
    bars2 = ax1.bar(x + width/2, used, width, label='实际使用', color='orange', alpha=0.7)
    
    # 标记瓶颈
    ax1.annotate('瓶颈！', xy=(1 + width/2, 480), xytext=(1.5, 520),
                arrowprops=dict(arrowstyle='->', color='red', lw=2),
                fontsize=12, color='red', fontweight='bold')
    
    ax1.set_ylabel('数量')
    ax1.set_title('资源使用情况分析\n（找到瓶颈约束）', fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels(resources)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (avail, use) in enumerate(zip(available, used)):
        ax1.text(i - width/2, avail + 10, f'{avail}', ha='center', fontweight='bold')
        ax1.text(i + width/2, use + 10, f'{use:.1f}', ha='center', fontweight='bold')
    
    # 图2：影子价格概念图
    resources_shadow = ['牛奶', '工人时间']
    shadow_prices = [0, 2.68]
    colors = ['lightcoral' if x == 0 else 'lightgreen' for x in shadow_prices]
    
    bars = ax2.bar(resources_shadow, shadow_prices, color=colors, alpha=0.7)
    ax2.set_ylabel('影子价格 (元/单位)')
    ax2.set_title('影子价格对比\n（增加1单位资源的价值）', fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签和解释
    for i, (resource, price) in enumerate(zip(resources_shadow, shadow_prices)):
        ax2.text(i, price + 0.1, f'{price}元', ha='center', fontweight='bold')
        if price == 0:
            ax2.text(i, -0.3, '不值钱\n(有剩余)', ha='center', fontsize=10, color='red')
        else:
            ax2.text(i, -0.3, '很值钱\n(是瓶颈)', ha='center', fontsize=10, color='green')
    
    # 图3：价格波动对利润的影响
    scenarios = ['价格-10%', '当前价格', '价格+10%']
    profits = [1159.2, 1288, 1416.8]
    
    bars = ax3.bar(scenarios, profits, color=['lightcoral', 'lightblue', 'lightgreen'], alpha=0.7)
    ax3.set_ylabel('利润 (元)')
    ax3.set_title('价格波动对利润的影响\n（生产方案保持不变）', fontweight='bold')
    ax3.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, profit in enumerate(profits):
        ax3.text(i, profit + 20, f'{profit:.1f}元', ha='center', fontweight='bold')
    
    # 添加变化幅度标注
    ax3.annotate('', xy=(0, 1159.2), xytext=(2, 1416.8),
                arrowprops=dict(arrowstyle='<->', color='purple', lw=2))
    ax3.text(1, 1300, '±10%波动\n利润变化约±10%', ha='center', 
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 图4：投资决策分析
    investments = ['增加牛奶\n(15元投资)', '雇临时工\n(2.68元/小时)']
    returns = [-15, 2.68]  # 牛奶投资亏损15元，临时工每小时赚2.68元
    colors = ['red' if x < 0 else 'green' for x in returns]
    
    bars = ax4.bar(investments, returns, color=colors, alpha=0.7)
    ax4.set_ylabel('净收益 (元)')
    ax4.set_title('投资决策分析\n（哪个投资更值得？）', fontweight='bold')
    ax4.grid(True, alpha=0.3)
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    # 添加数值标签和决策建议
    for i, (inv, ret) in enumerate(zip(investments, returns)):
        ax4.text(i, ret + (0.2 if ret > 0 else -0.2), f'{ret}元', ha='center', fontweight='bold')
        if ret < 0:
            ax4.text(i, ret - 1, '❌ 不建议', ha='center', fontsize=10, color='red')
        else:
            ax4.text(i, ret + 1, '✅ 可考虑', ha='center', fontsize=10, color='green')
    
    plt.tight_layout()
    plt.savefig('概念可视化图表.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_shadow_price_explanation():
    """
    创建影子价格概念的详细解释图
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # 左图：考试复习的例子
    resources = ['时间\n(小时)', '教材\n(本)', '精力\n(页)']
    limits = [10, 5, 100]
    used = [10, 3, 60]
    
    x = np.arange(len(resources))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, limits, width, label='限制', color='lightcoral', alpha=0.7)
    bars2 = ax1.bar(x + width/2, used, width, label='实际使用', color='lightblue', alpha=0.7)
    
    ax1.set_ylabel('数量')
    ax1.set_title('考试复习资源分析\n（类比理解影子价格）', fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels(resources)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 标记瓶颈
    ax1.annotate('瓶颈！\n时间用完了', xy=(0 + width/2, 10), xytext=(0.5, 12),
                arrowprops=dict(arrowstyle='->', color='red', lw=2),
                fontsize=10, color='red', fontweight='bold')
    
    # 添加数值标签
    for i, (limit, use) in enumerate(zip(limits, used)):
        ax1.text(i - width/2, limit + 2, f'{limit}', ha='center', fontweight='bold')
        ax1.text(i + width/2, use + 2, f'{use}', ha='center', fontweight='bold')
    
    # 右图：影子价格的含义
    items = ['多1本书', '多1小时', '多10页精力']
    values = [0, 5, 0]  # 假设多1小时能提高5分
    colors = ['lightcoral' if x == 0 else 'lightgreen' for x in values]
    
    bars = ax2.bar(items, values, color=colors, alpha=0.7)
    ax2.set_ylabel('价值 (分数提升)')
    ax2.set_title('增加资源的价值\n（影子价格示例）', fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    # 添加解释
    for i, (item, value) in enumerate(zip(items, values)):
        ax2.text(i, value + 0.2, f'{value}分', ha='center', fontweight='bold')
        if value == 0:
            ax2.text(i, -0.5, '没用\n(不是瓶颈)', ha='center', fontsize=9, color='red')
        else:
            ax2.text(i, -0.5, '有用\n(是瓶颈)', ha='center', fontsize=9, color='green')
    
    plt.tight_layout()
    plt.savefig('影子价格概念解释.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    print("=" * 50)
    print("为大一学生制作概念可视化图表")
    print("=" * 50)
    
    print("\n正在生成主要概念图表...")
    create_concept_visualization()
    
    print("\n正在生成影子价格概念解释图...")
    create_shadow_price_explanation()
    
    print("\n图表生成完成！")
    print("生成的文件：")
    print("1. 概念可视化图表.png - 主要概念的可视化")
    print("2. 影子价格概念解释.png - 影子价格的详细解释")
