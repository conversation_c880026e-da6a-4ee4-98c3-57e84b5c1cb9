"""
真正全面的最优解搜索
考虑所有可能的生产组合和深加工比例
不预设任何"全部"的假设
"""

import numpy as np
from scipy.optimize import linprog, minimize
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

def solve_unrestricted_model():
    """
    求解完全不受限制的线性规划模型
    允许任意的生产组合和深加工比例
    """
    print("=" * 60)
    print("真正的最优解搜索 - 考虑所有可能性")
    print("=" * 60)
    
    # 决策变量: [x1, x2, y1, y2, z1, z2]
    # x1: 甲类设备加工牛奶桶数
    # x2: 乙类设备加工牛奶桶数
    # y1: A1产品直接销售数量(kg)
    # y2: A2产品直接销售数量(kg)  
    # z1: A1产品深加工数量(kg)
    # z2: A2产品深加工数量(kg)
    
    # 目标函数: 最大化净利润
    # Z = 12*y1 + 8*y2 + (22*0.8-1.5)*z1 + (16*0.75-1.5)*z2
    # Z = 12*y1 + 8*y2 + 16.1*z1 + 10.5*z2
    c = [0, 0, -12, -8, -16.1, -10.5]  # 负号因为linprog求最小值
    
    # 不等式约束 A_ub * x <= b_ub
    A_ub = [
        # 原料约束: x1 + x2 <= 50
        [1, 1, 0, 0, 0, 0],
        
        # 劳动时间约束: 12*x1 + 8*x2 + 2*z1 + 2*z2 <= 480
        [12, 8, 0, 0, 2, 2],
        
        # 甲类设备约束: 3*x1 <= 100
        [3, 0, 0, 0, 0, 0],
    ]
    
    b_ub = [50, 480, 100]
    
    # 等式约束 A_eq * x = b_eq
    A_eq = [
        # A1产品平衡: y1 + z1 = 3*x1
        [-3, 0, 1, 0, 1, 0],
        
        # A2产品平衡: y2 + z2 = 4*x2  
        [0, -4, 0, 1, 0, 1],
    ]
    
    b_eq = [0, 0]
    
    # 变量边界 (所有变量非负)
    bounds = [(0, None)] * 6
    
    # 求解
    result = linprog(c, A_ub=A_ub, b_ub=b_ub, A_eq=A_eq, b_eq=b_eq, 
                     bounds=bounds, method='highs')
    
    if result.success:
        x1, x2, y1, y2, z1, z2 = result.x
        optimal_value = -result.fun
        
        print(f"✅ 找到真正的最优解！")
        print(f"\n📊 最优生产方案:")
        print(f"  甲类设备加工牛奶: {x1:.3f} 桶")
        print(f"  乙类设备加工牛奶: {x2:.3f} 桶")
        print(f"  A1直接销售: {y1:.3f} kg")
        print(f"  A2直接销售: {y2:.3f} kg") 
        print(f"  A1深加工: {z1:.3f} kg")
        print(f"  A2深加工: {z2:.3f} kg")
        print(f"  🎯 最大净利润: {optimal_value:.2f} 元/天")
        
        # 计算深加工比例
        a1_total = 3 * x1
        a2_total = 4 * x2
        
        if a1_total > 0:
            a1_deep_ratio = z1 / a1_total
            print(f"\n📈 生产策略分析:")
            print(f"  A1深加工比例: {a1_deep_ratio:.1%}")
        else:
            a1_deep_ratio = 0
            print(f"\n📈 生产策略分析:")
            print(f"  A1深加工比例: 不生产A1")
            
        if a2_total > 0:
            a2_deep_ratio = z2 / a2_total
            print(f"  A2深加工比例: {a2_deep_ratio:.1%}")
        else:
            a2_deep_ratio = 0
            print(f"  A2深加工比例: 不生产A2")
        
        # 验证约束
        print(f"\n🔍 约束使用情况:")
        material_used = x1 + x2
        labor_used = 12*x1 + 8*x2 + 2*z1 + 2*z2
        equipment_used = 3*x1
        
        print(f"  原料: {material_used:.2f}/50 桶 ({material_used/50:.1%})")
        print(f"  劳动时间: {labor_used:.2f}/480 小时 ({labor_used/480:.1%})")
        print(f"  甲类设备: {equipment_used:.2f}/100 kg ({equipment_used/100:.1%})")
        
        # 识别紧约束
        tight_constraints = []
        if abs(material_used - 50) < 0.01:
            tight_constraints.append("原料")
        if abs(labor_used - 480) < 0.01:
            tight_constraints.append("劳动时间")
        if abs(equipment_used - 100) < 0.01:
            tight_constraints.append("甲类设备")
            
        if tight_constraints:
            print(f"  🔴 紧约束: {', '.join(tight_constraints)}")
        else:
            print(f"  🟢 无紧约束")
        
        # 计算深加工产品
        b1_output = 0.8 * z1
        b2_output = 0.75 * z2
        print(f"\n🏭 最终产品:")
        print(f"  A1直接销售: {y1:.2f} kg")
        print(f"  A2直接销售: {y2:.2f} kg")
        print(f"  B1产品: {b1_output:.2f} kg")
        print(f"  B2产品: {b2_output:.2f} kg")
        
        # 利润构成分析
        profit_a1_direct = 12 * y1
        profit_a2_direct = 8 * y2
        profit_b1 = 16.1 * z1
        profit_b2 = 10.5 * z2
        
        print(f"\n💰 利润构成:")
        print(f"  A1直销利润: {profit_a1_direct:.2f} 元")
        print(f"  A2直销利润: {profit_a2_direct:.2f} 元")
        print(f"  B1深加工利润: {profit_b1:.2f} 元")
        print(f"  B2深加工利润: {profit_b2:.2f} 元")
        print(f"  总利润: {profit_a1_direct + profit_a2_direct + profit_b1 + profit_b2:.2f} 元")
        
        return result.x, optimal_value, tight_constraints
    else:
        print("❌ 求解失败!")
        print(f"失败原因: {result.message}")
        return None, None, None

def sensitivity_analysis_correct(optimal_solution, optimal_profit, tight_constraints):
    """
    基于正确最优解的敏感性分析
    """
    print("\n" + "=" * 60)
    print("基于真正最优解的敏感性分析")
    print("=" * 60)
    
    x1_opt, x2_opt, y1_opt, y2_opt, z1_opt, z2_opt = optimal_solution
    
    # 1. 原料投资分析
    print(f"\n📈 问题1: 投资15元增加1桶牛奶")
    
    # 重新求解增加1桶牛奶的情况
    c = [0, 0, -12, -8, -16.1, -10.5]
    A_ub = [
        [1, 1, 0, 0, 0, 0],      # x1 + x2 <= 51 (增加1桶)
        [12, 8, 0, 0, 2, 2],     # 劳动时间约束
        [3, 0, 0, 0, 0, 0],      # 设备约束
    ]
    b_ub = [51, 480, 100]
    A_eq = [[-3, 0, 1, 0, 1, 0], [0, -4, 0, 1, 0, 1]]
    b_eq = [0, 0]
    bounds = [(0, None)] * 6
    
    result_new = linprog(c, A_ub=A_ub, b_ub=b_ub, A_eq=A_eq, b_eq=b_eq, 
                        bounds=bounds, method='highs')
    
    if result_new.success:
        new_profit = -result_new.fun
        shadow_price_material = new_profit - optimal_profit
        print(f"  增加1桶牛奶后的利润: {new_profit:.2f} 元")
        print(f"  原料影子价格: {shadow_price_material:.2f} 元/桶")
        print(f"  投资净收益: {shadow_price_material - 15:.2f} 元")
        
        if shadow_price_material >= 15:
            print(f"  ✅ 建议投资 (每桶能增加{shadow_price_material:.2f}元利润)")
        else:
            print(f"  ❌ 不建议投资 (每桶只能增加{shadow_price_material:.2f}元利润)")
    
    # 2. 劳动力成本分析
    print(f"\n⏰ 问题2: 临时工工资标准")
    
    # 重新求解增加1小时劳动时间的情况
    A_ub_labor = [
        [1, 1, 0, 0, 0, 0],      # 原料约束
        [12, 8, 0, 0, 2, 2],     # x1 + x2 <= 481 (增加1小时)
        [3, 0, 0, 0, 0, 0],      # 设备约束
    ]
    b_ub_labor = [50, 481, 100]
    
    result_labor = linprog(c, A_ub=A_ub_labor, b_ub=b_ub_labor, A_eq=A_eq, b_eq=b_eq, 
                          bounds=bounds, method='highs')
    
    if result_labor.success:
        new_profit_labor = -result_labor.fun
        shadow_price_labor = new_profit_labor - optimal_profit
        print(f"  增加1小时劳动时间后的利润: {new_profit_labor:.2f} 元")
        print(f"  劳动时间影子价格: {shadow_price_labor:.2f} 元/小时")
        print(f"  临时工工资上限: {shadow_price_labor:.2f} 元/小时")
    
    # 3. 价格波动分析
    print(f"\n📊 问题3: B1、B2价格±10%波动分析")
    
    # +10%波动
    c_up = [0, 0, -12, -8, -16.1*1.1, -10.5*1.1]
    result_up = linprog(c_up, A_ub=A_ub, b_ub=b_ub, A_eq=A_eq, b_eq=b_eq, 
                       bounds=bounds, method='highs')
    
    # -10%波动  
    c_down = [0, 0, -12, -8, -16.1*0.9, -10.5*0.9]
    result_down = linprog(c_down, A_ub=A_ub, b_ub=b_ub, A_eq=A_eq, b_eq=b_eq, 
                         bounds=bounds, method='highs')
    
    if result_up.success and result_down.success:
        profit_up = -result_up.fun
        profit_down = -result_down.fun
        
        # 检查生产方案是否改变
        x1_up, x2_up, y1_up, y2_up, z1_up, z2_up = result_up.x
        x1_down, x2_down, y1_down, y2_down, z1_down, z2_down = result_down.x
        
        print(f"  价格+10%: 利润 {profit_up:.2f} 元")
        print(f"    生产方案: x1={x1_up:.1f}, x2={x2_up:.1f}, z1={z1_up:.1f}, z2={z2_up:.1f}")
        
        print(f"  价格-10%: 利润 {profit_down:.2f} 元")
        print(f"    生产方案: x1={x1_down:.1f}, x2={x2_down:.1f}, z1={z1_down:.1f}, z2={z2_down:.1f}")
        
        # 判断是否需要调整
        tolerance = 0.1  # 容差
        plan_changed = (abs(x1_up - x1_opt) > tolerance or abs(x2_up - x2_opt) > tolerance or
                       abs(z1_up - z1_opt) > tolerance or abs(z2_up - z2_opt) > tolerance or
                       abs(x1_down - x1_opt) > tolerance or abs(x2_down - x2_opt) > tolerance or
                       abs(z1_down - z1_opt) > tolerance or abs(z2_down - z2_opt) > tolerance)
        
        if plan_changed:
            print(f"  ⚠️  需要调整生产计划")
        else:
            print(f"  ✅ 无需调整生产计划")
        
        print(f"  利润变化范围: {profit_down:.1f} - {profit_up:.1f} 元")

def compare_with_previous_answer():
    """
    与之前错误答案的对比
    """
    print(f"\n" + "=" * 60)
    print("与之前分析的对比")
    print("=" * 60)
    
    print(f"之前的错误分析:")
    print(f"  方案: 纯甲类生产+全部深加工")
    print(f"  利润: 1288 元")
    print(f"  x1=26.67, x2=0, 全部A1深加工")
    
    print(f"\n真正的最优解:")
    print(f"  利润提升: {1730.4 - 1288:.1f} 元 (+{(1730.4-1288)/1288:.1%})")
    print(f"  这说明了线性规划中验证所有可能性的重要性！")

if __name__ == "__main__":
    # 求解真正的最优解
    optimal_solution, optimal_profit, tight_constraints = solve_unrestricted_model()
    
    if optimal_solution is not None:
        # 敏感性分析
        sensitivity_analysis_correct(optimal_solution, optimal_profit, tight_constraints)
        
        # 对比分析
        compare_with_previous_answer()
        
        print(f"\n" + "🎯" * 20)
        print(f"关键学习点:")
        print(f"1. 不要预设'全部'这样的假设")
        print(f"2. 线性规划会自动找到最优的混合策略")
        print(f"3. 最优解可能是反直觉的组合")
        print(f"4. 数学模型比人类直觉更可靠")
        print(f"🎯" * 20)
