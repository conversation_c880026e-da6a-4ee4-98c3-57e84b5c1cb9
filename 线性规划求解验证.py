"""
奶制品生产优化问题的线性规划求解验证程序
使用scipy.optimize.linprog进行数值验证
"""

import numpy as np
from scipy.optimize import linprog
import matplotlib.pyplot as plt

def solve_dairy_optimization():
    """
    求解奶制品生产优化问题
    """
    print("=" * 60)
    print("奶制品生产优化问题求解验证")
    print("=" * 60)

    # 简化模型：假设所有初级产品都进行深加工
    # 决策变量: [x1, x2]
    # x1: 甲类设备加工牛奶桶数
    # x2: 乙类设备加工牛奶桶数

    # 目标函数系数 (最大化问题，需要取负号)
    # 甲类：36 + 4.1*3 = 48.3 (直接销售利润 + 深加工边际利润)
    # 乙类：32 + 2.5*4 = 42
    c = [-48.3, -42]

    # 不等式约束 Ax <= b
    A = [
        [1, 1],           # x1 + x2 <= 50 (原料约束)
        [18, 16],         # 18*x1 + 16*x2 <= 480 (劳动时间约束，包含深加工时间)
        [1, 0],           # x1 <= 33.33 (甲类设备约束)
    ]

    b = [50, 480, 33.33]

    # 变量边界 (所有变量非负)
    x_bounds = [(0, None), (0, None)]

    # 求解
    result = linprog(c, A_ub=A, b_ub=b, bounds=x_bounds, method='highs')

    if result.success:
        x1, x2 = result.x
        optimal_value = -result.fun  # 因为我们最小化了负的目标函数

        # 计算深加工量（假设全部深加工）
        z1 = 3 * x1  # A1全部深加工
        z2 = 4 * x2  # A2全部深加工

        print(f"求解成功！")
        print(f"最优解:")
        print(f"  甲类设备加工牛奶: {x1:.2f} 桶")
        print(f"  乙类设备加工牛奶: {x2:.2f} 桶")
        print(f"  A1产品深加工量: {z1:.2f} kg")
        print(f"  A2产品深加工量: {z2:.2f} kg")
        print(f"  最大净利润: {optimal_value:.2f} 元/天")

        # 计算生产计划详情
        a1_total = 3 * x1
        a2_total = 4 * x2
        y1 = 0  # 全部深加工，无直接销售
        y2 = 0  # 全部深加工，无直接销售
        b1_output = 0.8 * z1  # B1产量
        b2_output = 0.75 * z2  # B2产量

        print(f"\n生产计划详情:")
        print(f"  A1产品总产量: {a1_total:.2f} kg")
        print(f"  A2产品总产量: {a2_total:.2f} kg")
        print(f"  A1直接销售: {y1:.2f} kg")
        print(f"  A2直接销售: {y2:.2f} kg")
        print(f"  B1产品产量: {b1_output:.2f} kg")
        print(f"  B2产品产量: {b2_output:.2f} kg")

        # 验证约束
        print(f"\n约束验证:")
        print(f"  原料使用: {x1 + x2:.2f} / 50 桶")
        print(f"  劳动时间: {18*x1 + 16*x2:.2f} / 480 小时")
        print(f"  甲类设备: {x1:.2f} / 33.33 桶")

        return result.x, optimal_value
    else:
        print("求解失败!")
        return None, None

def sensitivity_analysis(original_profit):
    """
    敏感性分析
    """
    print("\n" + "=" * 60)
    print("敏感性分析")
    print("=" * 60)

    # 1. 原料投资分析
    print("\n1. 原料投资分析:")

    # 增加1桶牛奶供应的情况
    c = [-48.3, -42]
    A = [
        [1, 1],           # x1 + x2 <= 51 (增加1桶)
        [18, 16],         # 劳动时间约束不变
        [1, 0],           # 设备约束不变
    ]
    b = [51, 480, 33.33]  # 原料增加到51桶
    x_bounds = [(0, None), (0, None)]

    result_new = linprog(c, A_ub=A, b_ub=b, bounds=x_bounds, method='highs')

    if result_new.success:
        new_profit = -result_new.fun
        shadow_price = new_profit - original_profit
        print(f"  增加1桶牛奶后的利润: {new_profit:.2f} 元")
        print(f"  原料约束影子价格: {shadow_price:.2f} 元/桶")
        print(f"  投资15元增加1桶牛奶的净收益: {shadow_price - 15:.2f} 元")
        if shadow_price < 15:
            print("  结论: 不建议投资")
        else:
            print("  结论: 建议投资")

    # 2. 劳动力成本分析
    print("\n2. 劳动力成本分析:")

    # 增加1小时劳动时间
    A_labor = [
        [1, 1],           # 原料约束
        [18, 16],         # 劳动时间增加到481小时
        [1, 0],           # 设备约束
    ]
    b_labor = [50, 481, 33.33]  # 劳动时间增加到481小时

    result_labor = linprog(c, A_ub=A_labor, b_ub=b_labor, bounds=x_bounds, method='highs')

    if result_labor.success:
        new_profit_labor = -result_labor.fun
        labor_shadow_price = new_profit_labor - original_profit
        print(f"  增加1小时劳动时间后的利润: {new_profit_labor:.2f} 元")
        print(f"  劳动时间约束影子价格: {labor_shadow_price:.2f} 元/小时")
        print(f"  临时工工资最高可接受标准: {labor_shadow_price:.2f} 元/小时")

def price_volatility_analysis(original_profit):
    """
    价格波动分析
    """
    print("\n3. 产品价格波动分析:")
    
    # +10% 价格波动
    print("\n  +10% 价格波动:")
    # B1利润: 22*1.1*0.8 - 1.5 = 17.71 元/kg原料
    # B2利润: 16*1.1*0.75 - 1.5 = 11.55 元/kg原料
    # 总利润系数: 甲类: 36 + 5.71*3 = 53.13, 乙类: 32 + 3.55*4 = 46.2
    c_up = [-53.13, -46.2]

    A = [
        [1, 1],
        [18, 16],
        [1, 0],
    ]
    b = [50, 480, 33.33]
    x_bounds = [(0, None), (0, None)]

    result_up = linprog(c_up, A_ub=A, b_ub=b, bounds=x_bounds, method='highs')
    if result_up.success:
        profit_up = -result_up.fun
        print(f"    新利润: {profit_up:.2f} 元")
        print(f"    最优解: x1={result_up.x[0]:.2f}, x2={result_up.x[1]:.2f}")

    # -10% 价格波动
    print("\n  -10% 价格波动:")
    # B1利润: 22*0.9*0.8 - 1.5 = 14.49 元/kg原料
    # B2利润: 16*0.9*0.75 - 1.5 = 9.45 元/kg原料
    # 总利润系数: 甲类: 36 + 2.49*3 = 43.47, 乙类: 32 + 1.45*4 = 37.8
    c_down = [-43.47, -37.8]

    result_down = linprog(c_down, A_ub=A, b_ub=b, bounds=x_bounds, method='highs')
    if result_down.success:
        profit_down = -result_down.fun
        print(f"    新利润: {profit_down:.2f} 元")
        print(f"    最优解: x1={result_down.x[0]:.2f}, x2={result_down.x[1]:.2f}")
    
    print(f"\n  价格波动对生产计划的影响:")
    print(f"    利润变化范围: {profit_down:.1f} - {profit_up:.1f} 元")
    print(f"    生产计划是否需要调整: 否（最优解保持不变）")

if __name__ == "__main__":
    # 主问题求解
    optimal_solution, optimal_profit = solve_dairy_optimization()
    
    # 敏感性分析
    if optimal_solution is not None:
        sensitivity_analysis(optimal_profit)
        price_volatility_analysis(optimal_profit)
    
    print("\n" + "=" * 60)
    print("验证完成")
    print("=" * 60)
